// import 'package:flutter/material.dart';
// import 'package:wicker/screens/add_product_screen.dart';
// import 'package:wicker/services/ecommerce_service.dart';
// import 'package:wicker/widgets/editable_product_card.dart';
// import 'package:wicker/widgets/neubrutalist_widgets.dart';

// class InventoryManagementScreen extends StatefulWidget {
//   final Map<String, dynamic> businessData;
//   const InventoryManagementScreen({super.key, required this.businessData});

//   @override
//   State<InventoryManagementScreen> createState() =>
//       _InventoryManagementScreenState();
// }

// class _InventoryManagementScreenState extends State<InventoryManagementScreen> {
//   final EcommerceService _ecommerceService = EcommerceService();
//   late Future<List<Map<String, dynamic>>> _productsFuture;

//   @override
//   void initState() {
//     super.initState();
//     _fetchProducts();
//   }

//   void _fetchProducts() {
//     final businessId = widget.businessData['_id']['\$oid'];
//     setState(() {
//       _productsFuture = _ecommerceService.getBusinessProducts(businessId);
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     final businessId = widget.businessData['_id']['\$oid'];

//     return Scaffold(
//       backgroundColor: const Color(0xFFFEF7F0),
//       appBar: PreferredSize(
//         preferredSize: const Size.fromHeight(60.0),
//         child: AppBar(
//           backgroundColor: Colors.white,
//           elevation: 0,
//           leading: IconButton(
//             icon: const Icon(Icons.arrow_back, color: Colors.black),
//             onPressed: () => Navigator.of(context).pop(),
//           ),
//           title: Text(
//             widget.businessData['business_name'] ?? 'My Inventory',
//             style: const TextStyle(
//               color: Colors.black,
//               fontWeight: FontWeight.bold,
//             ),
//           ),
//           bottom: PreferredSize(
//             preferredSize: const Size.fromHeight(4.0),
//             child: Container(color: Colors.black, height: 3.0),
//           ),
//         ),
//       ),
//       body: FutureBuilder<List<Map<String, dynamic>>>(
//         future: _productsFuture,
//         builder: (context, snapshot) {
//           if (snapshot.connectionState == ConnectionState.waiting) {
//             return const Center(child: CircularProgressIndicator());
//           }
//           if (snapshot.hasError) {
//             return Center(child: Text('Error: ${snapshot.error}'));
//           }
//           if (!snapshot.hasData || snapshot.data!.isEmpty) {
//             return Center(
//               child: NeuCard(
//                 backgroundColor: const Color(0xFFFFE66D),
//                 child: Padding(
//                   padding: const EdgeInsets.all(16.0),
//                   child: Column(
//                     mainAxisSize: MainAxisSize.min,
//                     children: [
//                       Icon(
//                         Icons.inventory_2_outlined,
//                         size: 48,
//                         color: Colors.grey[800],
//                       ),
//                       const SizedBox(height: 16),
//                       const Text(
//                         'NO PRODUCTS YET',
//                         style: TextStyle(
//                           fontSize: 18,
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),
//                       const SizedBox(height: 8),
//                       Text(
//                         'Tap the "+" button to add your first product.',
//                         style: TextStyle(color: Colors.grey[800]),
//                         textAlign: TextAlign.center,
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             );
//           }

//           final products = snapshot.data!;
//           return RefreshIndicator(
//             onRefresh: () async => _fetchProducts(),
//             child: ListView.builder(
//               padding: const EdgeInsets.symmetric(vertical: 8),
//               itemCount: products.length,
//               itemBuilder: (context, index) {
//                 return EditableProductCard(
//                   productData: products[index],
//                   onStockUpdated: _fetchProducts,
//                 );
//               },
//             ),
//           );
//         },
//       ),
//       floatingActionButton: FloatingActionButton.extended(
//         onPressed: () async {
//           final result = await Navigator.push<bool>(
//             context,
//             MaterialPageRoute(
//               builder: (context) => AddProductScreen(businessId: businessId),
//             ),
//           );
//           if (result == true) {
//             _fetchProducts();
//           }
//         },
//         label: const Text('Add Product'),
//         icon: const Icon(Icons.add),
//         backgroundColor: const Color(0xFF00D2D3),
//       ),
//     );
//   }
// }

import 'dart:io';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/screens/add_product_screen.dart';
import 'package:wicker/screens/sales_history_screen.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/services/inventory_service.dart';
import 'package:wicker/widgets/editable_product_card.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/neu_expandable_fab.dart';

class InventoryManagementScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const InventoryManagementScreen({super.key, required this.businessData});

  @override
  State<InventoryManagementScreen> createState() =>
      _InventoryManagementScreenState();
}

class _InventoryManagementScreenState extends State<InventoryManagementScreen> {
  final EcommerceService _ecommerceService = EcommerceService();
  final InventoryService _inventoryService = InventoryService();
  final ImagePicker _picker = ImagePicker();

  late Future<List<Map<String, dynamic>>> _productsFuture;
  Future<List<dynamic>>? _alertsFuture;
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() {
    _fetchProducts();
    _fetchAlerts();
  }

  void _fetchProducts() {
    final businessId = widget.businessData['_id']['\$oid'];
    setState(() {
      _productsFuture = _ecommerceService.getBusinessProducts(businessId);
    });
  }

  void _fetchAlerts() {
    setState(() {
      _alertsFuture = _inventoryService.getRestockAlerts();
    });
  }

  Future<void> _scanShelf() async {
    final XFile? imageFile = await _picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 70,
    );
    if (imageFile == null) return;

    setState(() => _isScanning = true);

    try {
      final results = await _inventoryService.analyzeShelfImage(
        File(imageFile.path),
      );
      if (mounted) {
        await _showShelfScanResults(results['inventory'] as List<dynamic>);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('AI Scan Failed: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isScanning = false);
      }
    }
  }

  Future<void> _showShelfScanResults(List<dynamic> inventory) async {
    if (inventory.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('The AI did not find any of your products.'),
        ),
      );
      return;
    }

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI Scan Results'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: inventory.length,
            itemBuilder: (context, index) {
              final item = inventory[index];
              return ListTile(
                title: Text(item['product_name']),
                trailing: Text('Found: ${item['quantity']}'),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Update Stock'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final products = await _productsFuture;
      for (var foundItem in inventory) {
        final product = products.firstWhere(
          (p) => p['product_name'] == foundItem['product_name'],
          orElse: () => {},
        );
        if (product.isNotEmpty) {
          final productId = product['_id']['\$oid'];
          await _ecommerceService.updateProductStock(
            productId,
            foundItem['quantity'],
          );
        }
      }
      _fetchProducts();
    }
  }

  // --- NEW: Helper function to navigate to Add Product screen ---
  void _navigateToAddProduct() async {
    final businessId = widget.businessData['_id']['\$oid'];
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => AddProductScreen(businessId: businessId),
      ),
    );
    if (result == true) {
      _fetchData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final businessId = widget.businessData['_id']['\$oid'];

    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            widget.businessData['business_name'] ?? 'My Inventory',
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(EvaIcons.barChart2Outline, color: Colors.black),
              tooltip: 'Sales History',
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SalesHistoryScreen(),
                ),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Column(
        children: [
          _buildAlertsSection(),
          Expanded(
            child: FutureBuilder<List<Map<String, dynamic>>>(
              future: _productsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(
                    child: NeuCard(
                      backgroundColor: const Color(0xFFFFE66D),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.inventory_2_outlined,
                              size: 48,
                              color: Colors.grey[800],
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'NO PRODUCTS YET',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Tap the "+" button to add your first product.',
                              style: TextStyle(color: Colors.grey[800]),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }

                final products = snapshot.data!;
                return RefreshIndicator(
                  onRefresh: () async => _fetchProducts(),
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    itemCount: products.length,
                    itemBuilder: (context, index) {
                      return EditableProductCard(
                        productData: products[index],
                        onStockUpdated: _fetchData,
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: NeuExpandableFab(
        distance: 112.0,
        actions: [
          FabAction(onPressed: _navigateToAddProduct, icon: Icons.add),
          FabAction(
            onPressed: _isScanning ? () {} : _scanShelf,
            icon: EvaIcons.camera,
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsSection() {
    return FutureBuilder<List<dynamic>>(
      future: _alertsFuture,
      builder: (context, snapshot) {
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const SizedBox.shrink();
        }
        final alerts = snapshot.data!;
        return NeuCard(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          backgroundColor: const Color(0xFFFF6B6B),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(EvaIcons.alertTriangle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    "Restock Alerts",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ...alerts
                  .map(
                    (alert) => Text(
                      "• ${alert['product_name']}: Only ${alert['current_stock']} left (~${alert['days_remaining']} days).",
                      style: const TextStyle(color: Colors.white),
                    ),
                  )
                  .toList(),
            ],
          ),
        );
      },
    );
  }
}
