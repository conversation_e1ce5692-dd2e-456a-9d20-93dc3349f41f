import 'dart:convert';
import 'dart:io';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class InventoryService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<List<dynamic>> getRestockAlerts() async {
    final baseUrl = await _config.getBaseUrl();
    try {
      final response = await _client.get(
        Uri.parse('$baseUrl/api/inventory/restock-alerts'),
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Failed to load restock alerts');
      }
    } catch (e) {
      print('Get restock alerts error: $e');
      rethrow;
    }
  }

  Future<Map<String, dynamic>> analyzeShelfImage(File image) async {
    final baseUrl = await _config.getBaseUrl();
    try {
      // Convert image to Base64 string for JSON transport
      final bytes = await image.readAsBytes();
      final String base64Image = base64Encode(bytes);

      final response = await _client.post(
        Uri.parse('$baseUrl/api/inventory/analyze-shelf'),
        body: jsonEncode({'image': base64Image}),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        final body = jsonDecode(response.body);
        throw Exception(body['msg'] ?? 'Failed to analyze shelf image');
      }
    } catch (e) {
      print('Analyze shelf image error: $e');
      rethrow;
    }
  }
}
