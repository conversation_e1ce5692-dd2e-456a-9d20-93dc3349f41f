from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime
import base64
import json

inventory_bp = Blueprint('inventory_bp', __name__)

@inventory_bp.route('/analyze-shelf', methods=['POST'])
@jwt_required()
def analyze_shelf_image():
    """
    Receives an image of a shelf, uses an AI vision model to identify
    and count known products, and returns a suggested inventory update.
    """
    db = inventory_bp.db
    # In a production app, you might get the openai_client from the app context
    from app import openai_client 
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    if 'image' not in data:
        return jsonify({"msg": "No image provided"}), 400

    image_data = data['image']
    
    # Find the user's business and their existing products
    business = db.businesses.find_one({"owner_id": current_user_id})
    if not business:
        return jsonify({"msg": "You must have a business to use this feature."}), 404

    products = list(db.products.find({"business_id": business['_id']}, {"product_name": 1}))
    product_names = [product['product_name'] for product in products]

    if not product_names:
        return jsonify({"msg": "You have no products in your inventory to match against."}), 400

    prompt = f"""
    You are an expert inventory management assistant for a small shop in Accra, Ghana.
    Analyze the attached image of a shelf containing various products.
    Your task is to identify and count only the products from the following list: {", ".join(product_names)}.

    Your response MUST be a valid JSON object containing a single key, "inventory",
    which is a list of objects. Each object should have two keys: "product_name" and "quantity".

    Example response format:
    {{
      "inventory": [
        {{"product_name": "Bel-Aqua Water", "quantity": 12}},
        {{"product_name": "Fanice", "quantity": 8}}
      ]
    }}

    If you cannot identify any of the listed products, return an empty "inventory" list.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        },
                    ],
                }
            ],
            max_tokens=300,
        )
        
        # Sometimes the model returns the JSON wrapped in markdown, so we clean it
        cleaned_response = response.choices[0].message.content.strip().replace('```json', '').replace('```', '')
        ai_response = json.loads(cleaned_response)
        return jsonify(ai_response), 200

    except Exception as e:
        print(f"AI Shelf Analysis Failed: {e}")
        return jsonify({"msg": "Failed to analyze image with AI.", "error": str(e)}), 500

@inventory_bp.route('/restock-alerts', methods=['GET'])
@jwt_required()
def get_restock_alerts():
    """
    Analyzes sales data to generate predictive restock alerts for a vendor.
    """
    db = inventory_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    
    # 1. Find the user's business
    business = db.businesses.find_one({"owner_id": current_user_id})
    if not business:
        return jsonify([]), 200 # Return empty list if no business
        
    # 2. Get all products for the business
    products = list(db.products.find({"business_id": business['_id']}))
    
    alerts = []
    
    lookback_days = 30
    start_date = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=lookback_days)
    alert_threshold_days = 7

    for product in products:
        product_id = product['_id']
        current_stock = product.get('stock_count', 0)
        
        # 3. Calculate sales velocity for this product
        pipeline = [
            {'$match': {
                'seller_id': current_user_id,
                'status': 'completed',
                'created_at': {'$gte': start_date}
            }},
            {'$unwind': '$items'},
            {'$match': {'items.product_id': product_id}},
            {'$group': {
                '_id': '$items.product_id',
                'total_sold': {'$sum': '$items.quantity'}
            }}
        ]
        
        sales_data = list(db.orders.aggregate(pipeline))
        
        if not sales_data:
            continue
            
        total_sold = sales_data[0]['total_sold']
        avg_daily_sales = total_sold / lookback_days
        
        if avg_daily_sales <= 0:
            continue
            
        days_remaining = current_stock / avg_daily_sales
        
        if days_remaining < alert_threshold_days:
            alerts.append({
                'product_id': str(product_id),
                'product_name': product.get('product_name', 'N/A'),
                'current_stock': current_stock,
                'days_remaining': round(days_remaining, 1)
            })

    return jsonify(alerts), 200