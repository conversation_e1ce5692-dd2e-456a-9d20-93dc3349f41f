from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime
import base64
import json
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

inventory_bp = Blueprint('inventory_bp', __name__)

@inventory_bp.route('/analyze-shelf', methods=['POST'])
@jwt_required()
def analyze_shelf_image():
    """
    Receives an image of a shelf, uses an AI vision model to identify
    and count known products, and returns a suggested inventory update.
    """
    try:
        logger.debug("=== Starting analyze_shelf_image endpoint ===")
        db = inventory_bp.db
        logger.debug("Database connection established")

        # In a production app, you might get the openai_client from the app context
        from app import openai_client
        logger.debug("OpenAI client imported")

        current_user_id = ObjectId(get_jwt_identity())
        logger.debug(f"Current user ID: {current_user_id}")

        data = request.get_json()
        logger.debug(f"Received data keys: {list(data.keys()) if data else 'None'}")

        if 'image' not in data:
            logger.error("No image provided in request")
            return jsonify({"msg": "No image provided"}), 400

        image_data = data['image']
        logger.debug(f"Image data length: {len(image_data) if image_data else 0}")

        # Find the user's business and their existing products
        logger.debug("Looking up user's business...")
        business = db.businesses.find_one({"owner_id": current_user_id})
        if not business:
            logger.error(f"No business found for user {current_user_id}")
            return jsonify({"msg": "You must have a business to use this feature."}), 404

        logger.debug(f"Found business: {business['_id']}")
        products = list(db.products.find({"business_id": business['_id']}, {"product_name": 1}))
        product_names = [product['product_name'] for product in products]
        logger.debug(f"Found {len(products)} products: {product_names}")

        if not product_names:
            logger.error("No products found in inventory")
            return jsonify({"msg": "You have no products in your inventory to match against."}), 400

        prompt = f"""
        You are an expert inventory management assistant for a small shop in Accra, Ghana.
        Analyze the attached image of a shelf containing various products.
        Your task is to identify and count only the products from the following list: {", ".join(product_names)}.

        Your response MUST be a valid JSON object containing a single key, "inventory",
        which is a list of objects. Each object should have two keys: "product_name" and "quantity".

        Example response format:
        {{
          "inventory": [
            {{"product_name": "Bel-Aqua Water", "quantity": 12}},
            {{"product_name": "Fanice", "quantity": 8}}
          ]
        }}

        If you cannot identify any of the listed products, return an empty "inventory" list.
        """
        logger.debug("Prompt created, calling OpenAI API...")

        try:
            response = openai_client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_data}"
                                }
                            },
                        ],
                    }
                ],
                max_tokens=300,
            )
            logger.debug("OpenAI API call successful")

            # Sometimes the model returns the JSON wrapped in markdown, so we clean it
            raw_content = response.choices[0].message.content
            logger.debug(f"Raw AI response: {raw_content}")

            cleaned_response = raw_content.strip().replace('```json', '').replace('```', '')
            logger.debug(f"Cleaned response: {cleaned_response}")

            ai_response = json.loads(cleaned_response)
            logger.debug(f"Parsed AI response: {ai_response}")
            return jsonify(ai_response), 200

        except json.JSONDecodeError as e:
            logger.error(f"JSON decode error: {e}")
            logger.error(f"Raw response was: {response.choices[0].message.content if 'response' in locals() else 'No response'}")
            return jsonify({"msg": "Failed to parse AI response as JSON.", "error": str(e)}), 500
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            return jsonify({"msg": "Failed to analyze image with AI.", "error": str(e)}), 500

    except Exception as e:
        logger.error(f"General error in analyze_shelf_image: {e}")
        return jsonify({"msg": "Internal server error", "error": str(e)}), 500

@inventory_bp.route('/restock-alerts', methods=['GET'])
@jwt_required()
def get_restock_alerts():
    """
    Analyzes sales data to generate predictive restock alerts for a vendor.
    """
    try:
        logger.debug("=== Starting get_restock_alerts endpoint ===")
        db = inventory_bp.db
        logger.debug("Database connection established")

        current_user_id = ObjectId(get_jwt_identity())
        logger.debug(f"Current user ID: {current_user_id}")

        # 1. Find the user's business
        logger.debug("Looking up user's business...")
        business = db.businesses.find_one({"owner_id": current_user_id})
        if not business:
            logger.warning(f"No business found for user {current_user_id}")
            return jsonify([]), 200 # Return empty list if no business

        logger.debug(f"Found business: {business['_id']}")

        # 2. Get all products for the business
        logger.debug("Fetching products for business...")
        products = list(db.products.find({"business_id": business['_id']}))
        logger.debug(f"Found {len(products)} products")

        alerts = []

        lookback_days = 30
        start_date = datetime.datetime.now(datetime.timezone.utc) - datetime.timedelta(days=lookback_days)
        alert_threshold_days = 7
        logger.debug(f"Analysis parameters: lookback_days={lookback_days}, alert_threshold_days={alert_threshold_days}")

        for product in products:
            product_id = product['_id']
            current_stock = product.get('stock_count', 0)
            product_name = product.get('product_name', 'N/A')
            logger.debug(f"Analyzing product: {product_name} (ID: {product_id}, stock: {current_stock})")

            # 3. Calculate sales velocity for this product
            pipeline = [
                {'$match': {
                    'seller_id': current_user_id,
                    'status': 'completed',
                    'created_at': {'$gte': start_date}
                }},
                {'$unwind': '$items'},
                {'$match': {'items.product_id': product_id}},
                {'$group': {
                    '_id': '$items.product_id',
                    'total_sold': {'$sum': '$items.quantity'}
                }}
            ]
            logger.debug(f"Sales aggregation pipeline: {pipeline}")

            sales_data = list(db.orders.aggregate(pipeline))
            logger.debug(f"Sales data for {product_name}: {sales_data}")

            if not sales_data:
                logger.debug(f"No sales data found for {product_name}")
                continue

            total_sold = sales_data[0]['total_sold']
            avg_daily_sales = total_sold / lookback_days
            logger.debug(f"Product {product_name}: total_sold={total_sold}, avg_daily_sales={avg_daily_sales}")

            if avg_daily_sales <= 0:
                logger.debug(f"Zero or negative daily sales for {product_name}")
                continue

            days_remaining = current_stock / avg_daily_sales
            logger.debug(f"Product {product_name}: days_remaining={days_remaining}")

            if days_remaining < alert_threshold_days:
                alert = {
                    'product_id': str(product_id),
                    'product_name': product_name,
                    'current_stock': current_stock,
                    'days_remaining': round(days_remaining, 1)
                }
                logger.debug(f"Adding alert for {product_name}: {alert}")
                alerts.append(alert)

        logger.debug(f"Generated {len(alerts)} alerts")
        return jsonify(alerts), 200

    except Exception as e:
        logger.error(f"Error in get_restock_alerts: {e}")
        return jsonify({"msg": "Internal server error", "error": str(e)}), 500